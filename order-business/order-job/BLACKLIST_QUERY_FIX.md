# 被拉黑模特查询逻辑修复说明

## 问题描述

原来的 `getBlacklistedPreselectModels()` 方法实现有误，存在以下问题：

1. **查询方式错误**：通过 `ModelInfoVO.getBlacklistUserIds()` 查询黑名单，这种方式不正确
2. **性能低效**：需要查询所有活跃预选模特，然后逐一检查黑名单关系
3. **逻辑复杂**：需要多次数据查询和内存中的关联匹配

## 修复方案

### 🔧 核心修复

重写了 `getBlacklistedPreselectModels()` 方法，改为直接查询 `user_model_blacklist` 表并通过数据库关联查询。

### 📊 数据库表关系

```
order_video (订单表)
    ↓ 一对多
order_video_match (匹配单表)
    ↓ 一对多  
order_video_match_preselect_model (预选模特表)
    ↓ 关联条件
user_model_blacklist (模特黑名单表)
```

### 🔍 正确的查询逻辑

#### 关联条件
1. **商家用户ID匹配**：`user_model_blacklist.biz_user_id` = `order_video.create_order_biz_user_id`
2. **模特ID匹配**：`user_model_blacklist.model_id` = `order_video_match_preselect_model.model_id`
3. **活跃状态过滤**：`order_video_match_preselect_model.status` IN (未对接, 已对接)

#### SQL 查询实现
```sql
SELECT 
    ovmpm.*
FROM 
    order_video_match_preselect_model ovmpm
    INNER JOIN order_video_match ovm ON ovmpm.match_id = ovm.id
    INNER JOIN order_video ov ON ovm.video_id = ov.id AND (ov.rollback_id <=> ovm.rollback_id)
    INNER JOIN user_model_blacklist umb ON umb.model_id = ovmpm.model_id 
        AND umb.biz_user_id = ov.create_order_biz_user_id
WHERE 
    ovmpm.status IN (0, 1)  -- 未对接, 已对接
    AND ovmpm.select_status != 6  -- 排除已取消申请
    AND ovm.status = 0  -- 匹配单正常状态
```

## 📁 修改的文件

### 1. Mapper 接口
**文件**：`OrderVideoMatchPreselectModelMapper.java`

**新增方法**：
```java
/**
 * 查询被商家拉黑的预选模特记录
 * 通过关联 user_model_blacklist 表查询被对应商家拉黑的预选模特
 * 
 * @return 被商家拉黑的预选模特列表
 */
List<OrderVideoMatchPreselectModel> selectBlacklistedPreselectModels();
```

### 2. Mapper XML
**文件**：`OrderVideoMatchPreselectModelMapper.xml`

**新增查询**：
- 实现了完整的多表关联查询
- 确保黑名单匹配的是创建订单的商家用户
- 只查询活跃状态的预选模特记录

### 3. Service 实现
**文件**：`OrderVideoMatchPreselectModelServiceImpl.java`

**重写方法**：`getBlacklistedPreselectModels()`
- 简化为直接调用 Mapper 方法
- 移除了复杂的内存关联逻辑
- 提高了查询效率和准确性

## 🚀 性能优化效果

### 优化前
```
1. 查询所有活跃预选模特记录
2. 获取预选模特的商家信息
3. 获取模特信息（包含黑名单）
4. 在内存中进行关联匹配
5. 筛选被商家拉黑的预选模特
```

### 优化后
```
1. 直接通过数据库关联查询被商家拉黑的预选模特
```

### 性能提升
- ✅ **查询次数减少**：从多次查询减少到单次查询
- ✅ **内存使用优化**：无需在内存中进行大量数据关联
- ✅ **查询精度提升**：直接通过数据库关联确保准确性
- ✅ **代码简化**：逻辑更清晰，易于维护

## 🔍 关键技术点

### 1. 多表关联查询
使用 INNER JOIN 确保只查询真正被拉黑的预选模特：
- `order_video_match_preselect_model` ← 主表
- `order_video_match` ← 匹配单信息
- `order_video` ← 订单信息（获取创建订单的商家用户ID）
- `user_model_blacklist` ← 黑名单信息

### 2. 精确的关联条件
```sql
-- 模特ID匹配
umb.model_id = ovmpm.model_id 
-- 商家用户ID匹配（确保是对应商家拉黑的模特）
AND umb.biz_user_id = ov.create_order_biz_user_id
```

### 3. 状态过滤
```sql
-- 只处理活跃状态的预选模特
ovmpm.status IN (0, 1)  -- 未对接, 已对接
-- 排除已取消申请的记录
AND ovmpm.select_status != 6
-- 确保匹配单状态正常
AND ovm.status = 0
```

## ✅ 验证要点

### 功能验证
- [x] 正确识别被对应商家拉黑的模特
- [x] 不会误判其他商家拉黑的模特
- [x] 只处理活跃状态的预选模特
- [x] 排除已取消申请的记录

### 性能验证
- [x] 查询效率显著提升
- [x] 内存使用优化
- [x] 数据库压力减少
- [x] 查询结果准确性提高

### 数据一致性
- [x] 确保黑名单匹配的是创建订单的商家用户
- [x] 正确处理订单回退场景（rollback_id 匹配）
- [x] 状态过滤逻辑正确

## 📈 总结

这次修复解决了被拉黑模特查询逻辑的根本问题：

1. **准确性提升**：通过数据库关联确保查询结果的准确性
2. **性能优化**：大幅减少查询次数和内存使用
3. **代码简化**：逻辑更清晰，易于理解和维护
4. **扩展性增强**：为后续功能扩展提供了良好的基础

修复后的实现更符合数据库设计原则，充分利用了关系型数据库的关联查询能力，是一个更加高效和可靠的解决方案。
