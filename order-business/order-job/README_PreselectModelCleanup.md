# 预选模特清理定时任务

## 概述

`PreselectModelCleanupHandler` 是一个XXL-Job定时任务，用于自动清理不符合条件的预选模特数据。该任务每5分钟执行一次，确保预选模特池中的数据质量。

## 功能说明

### 清理条件

任务会自动清理以下情况的预选模特：

1. **模特状态异常**
   - 模特状态不是正常合作（非 `ModelStatusEnum.NORMAL`）
   - 包括：暂停合作、取消合作、行程中等状态

2. **模特不展示**
   - 模特的 `is_show` 字段为 0（不展示状态）

3. **商家拉黑**
   - 被对应商家拉入黑名单的模特

4. **逾期订单**
   - 有逾期未完成订单的模特

### 处理逻辑

- 只处理状态为"未对接"（UN_JOINTED）或"已对接"（JOINTED）的预选模特
- 排除已经是"已淘汰"（OUT）状态的记录
- 根据不同清理原因设置相应的备注信息
- 自动更新相关的匹配单标记

## 技术实现

### 核心组件

1. **Handler类**: `PreselectModelCleanupHandler`
   - 位置: `order-business/order-job/src/main/java/com/wnkx/order/job/handler/`
   - 任务名称: `cleanupInvalidPreselectModels`

2. **Service方法**: `IOrderVideoMatchPreselectModelService.cleanupInvalidPreselectModels()`
   - 实现类: `OrderVideoMatchPreselectModelServiceImpl`

### 关键特性

- **事务支持**: 使用 `@Transactional` 注解确保数据一致性
- **异常处理**: 完整的异常捕获和日志记录
- **性能优化**: 批量查询和更新操作
- **详细日志**: 记录清理统计和执行时间

## XXL-Job配置

### 任务配置参数

```
任务名称: cleanupInvalidPreselectModels
Cron表达式: 0 */5 * * * ?  (每5分钟执行一次)
运行模式: BEAN
JobHandler: cleanupInvalidPreselectModels
负责人: 系统管理员
报警邮件: 根据需要配置
```

### 配置步骤

1. 登录XXL-Job管理控制台
2. 进入任务管理页面
3. 点击"新增"按钮
4. 填写上述配置参数
5. 保存并启动任务

## 监控和日志

### 日志输出

任务执行时会输出以下日志信息：

- 任务开始执行时间
- 检查的预选模特记录数量
- 清理的记录数量和详细统计
- 任务执行耗时
- 异常信息（如有）

### 监控指标

- **执行频率**: 每5分钟
- **执行时间**: 通常在几秒到几十秒之间
- **清理数量**: 根据实际数据情况而定
- **成功率**: 应保持在99%以上

## 注意事项

### 数据安全

- 任务只会将预选模特状态更新为"已淘汰"，不会删除数据
- 所有操作都在事务中执行，确保数据一致性
- 清理操作会记录详细的原因和时间

### 性能考虑

- 任务使用批量操作减少数据库压力
- 合理的执行频率避免对系统性能造成影响
- 如果数据量过大，可以考虑调整执行频率

### 业务影响

- 清理操作不会影响已经在进行中的订单
- 被清理的预选模特可以重新添加（如果条件满足）
- 清理操作会自动更新相关的匹配单状态

## 故障排查

### 常见问题

1. **任务执行失败**
   - 检查数据库连接
   - 查看详细错误日志
   - 确认相关服务是否正常

2. **清理数量异常**
   - 检查模特数据状态
   - 确认业务逻辑是否正确
   - 查看清理统计日志

3. **性能问题**
   - 监控任务执行时间
   - 检查数据库性能
   - 考虑优化查询条件

### 日志查看

```bash
# 查看任务执行日志
tail -f logs/order-job/order-job.log | grep "PreselectModelCleanup"

# 查看XXL-Job调度日志
# 在XXL-Job管理控制台的调度日志页面查看
```

## 版本历史

- **v1.0** (2025-01-31): 初始版本，实现基本的预选模特清理功能
