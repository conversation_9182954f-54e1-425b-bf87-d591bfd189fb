package com.wnkx.order.job.handler;

import com.wnkx.order.service.IOrderVideoMatchPreselectModelService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 预选模特清理定时任务Handler
 * 
 * <AUTHOR>
 * @date 2025-01-31
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class PreselectModelCleanupHandler {

    private final IOrderVideoMatchPreselectModelService orderVideoMatchPreselectModelService;

    /**
     * 清理不符合条件的预选模特数据
     * 每5分钟执行一次，自动清理以下情况的预选模特：
     * 1. 模特状态不是正常合作的（ModelStatusEnum.NORMAL）
     * 2. 模特的 is_show 字段为 0（不展示）
     * 3. 被商家拉黑的模特
     * 4. 有逾期订单的模特
     */
    @XxlJob("cleanupInvalidPreselectModels")
    @Transactional(rollbackFor = Exception.class)
    public void cleanupInvalidPreselectModels() {
        XxlJobHelper.log("任务[清理不符合条件的预选模特数据]开始执行...");
        
        try {
            // 记录任务开始时间
            long startTime = System.currentTimeMillis();
            
            // 执行清理逻辑
            int cleanupCount = orderVideoMatchPreselectModelService.cleanupInvalidPreselectModels();
            
            // 计算执行时间
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 记录执行结果
            XxlJobHelper.log("任务执行完成，清理了 {} 条不符合条件的预选模特记录", cleanupCount);
            XxlJobHelper.log("任务执行耗时: {} ms", executionTime);
            
            // 设置任务执行结果
            if (cleanupCount > 0) {
                XxlJobHelper.handleSuccess("成功清理 " + cleanupCount + " 条预选模特记录，耗时 " + executionTime + " ms");
            } else {
                XxlJobHelper.handleSuccess("没有需要清理的预选模特记录，耗时 " + executionTime + " ms");
            }
            
        } catch (Exception e) {
            log.error("清理不符合条件的预选模特数据任务执行失败", e);
            XxlJobHelper.log("任务执行失败: {}", e.getMessage());
            XxlJobHelper.handleFail("任务执行失败: " + e.getMessage());
            
            // 重新抛出异常以触发事务回滚
            throw new RuntimeException("清理预选模特数据任务执行失败", e);
        }
    }
}
